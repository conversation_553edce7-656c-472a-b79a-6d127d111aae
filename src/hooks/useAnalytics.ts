import { useCallback, useEffect } from 'react';
import { analytics, AnalyticsEventType } from '../services/apiService';

/**
 * Custom hook for analytics tracking
 * Provides easy-to-use methods for tracking various user actions
 */
export const useAnalytics = () => {
  // Track page views automatically when component mounts
  useEffect(() => {
    const trackPageView = async () => {
      await analytics.trackPageView(document.title, {
        referrer: document.referrer,
        timestamp: new Date().toISOString()
      });
    };

    trackPageView();
  }, []);

  // Track general events
  const trackEvent = useCallback(async (
    eventType: AnalyticsEventType,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackEvent(eventType, metadata);
  }, []);

  // Track file upload events
  const trackFileUpload = useCallback(async (
    fileName: string,
    fileSize: number,
    fileType: string,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackFileUpload(fileName, fileSize, fileType, metadata);
  }, []);

  // Track chart events
  const trackChartEvent = useCallback(async (
    eventType: AnalyticsEventType,
    chartType?: string,
    chartId?: string | number,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent(eventType, chartType, chartId, metadata);
  }, []);

  // Track chart view
  const trackChartView = useCallback(async (
    chartType: string,
    chartId: string | number,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent('CHART_VIEW', chartType, chartId, {
      ...metadata,
      view_timestamp: new Date().toISOString()
    });
  }, []);

  // Track chart download
  const trackChartDownload = useCallback(async (
    chartType: string,
    chartId: string | number,
    downloadFormat: string = 'png',
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent('CHART_DOWNLOAD', chartType, chartId, {
      ...metadata,
      download_format: downloadFormat,
      download_timestamp: new Date().toISOString()
    });
  }, []);

  // Track chart copy
  const trackChartCopy = useCallback(async (
    chartType: string,
    chartId: string | number,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent('CHART_COPY', chartType, chartId, {
      ...metadata,
      copy_timestamp: new Date().toISOString()
    });
  }, []);

  // Track chart save
  const trackChartSave = useCallback(async (
    chartType: string,
    chartId: string | number,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent('CHART_SAVE', chartType, chartId, {
      ...metadata,
      save_timestamp: new Date().toISOString()
    });
  }, []);

  // Track navigation events
  const trackNavigation = useCallback(async (
    fromPage: string,
    toPage: string,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackEvent('NAVIGATION', {
      ...metadata,
      from_page: fromPage,
      to_page: toPage,
      navigation_timestamp: new Date().toISOString()
    });
  }, []);

  // Track additional charts view
  const trackAdditionalChartsView = useCallback(async (
    groupName: string,
    totalCharts: number,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackEvent('ADDITIONAL_CHARTS_VIEW', {
      ...metadata,
      group_name: groupName,
      total_charts: totalCharts,
      view_timestamp: new Date().toISOString()
    });
  }, []);

  // Track error events
  const trackError = useCallback(async (
    errorType: string,
    errorMessage: string,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackEvent('ERROR_OCCURRED', {
      ...metadata,
      error_type: errorType,
      error_message: errorMessage,
      error_timestamp: new Date().toISOString()
    });
  }, []);

  // Track insights request
  const trackInsightsRequest = useCallback(async (
    chartType: string,
    chartId: string | number,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent('CHART_INSIGHTS_REQUEST', chartType, chartId, {
      ...metadata,
      request_timestamp: new Date().toISOString()
    });
  }, []);

  // Track executive summary view
  const trackExecutiveSummaryView = useCallback(async (
    chartType: string,
    chartId: string | number,
    insightsCount: number = 0,
    metadata: Record<string, any> = {}
  ) => {
    await analytics.trackChartEvent('EXECUTIVE_SUMMARY_VIEW', chartType, chartId, {
      ...metadata,
      insights_count: insightsCount,
      view_timestamp: new Date().toISOString()
    });
  }, []);

  return {
    // General tracking
    trackEvent,
    trackError,
    trackNavigation,
    
    // File tracking
    trackFileUpload,
    
    // Chart tracking
    trackChartEvent,
    trackChartView,
    trackChartDownload,
    trackChartCopy,
    trackChartSave,
    trackInsightsRequest,
    trackExecutiveSummaryView,
    
    // Page tracking
    trackAdditionalChartsView,
    
    // Analytics instance for advanced usage
    analytics
  };
};

export default useAnalytics;
