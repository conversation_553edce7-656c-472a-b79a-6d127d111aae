// src/services/apiService.ts

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// UUID generator
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// Device ID fetcher
export function getDeviceId(): string {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = generateUUID();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

// Session ID fetcher (unique per login session)
export function getSessionId(): string {
  let sessionId = sessionStorage.getItem('session_id');
  if (!sessionId) {
    sessionId = generateUUID();
    sessionStorage.setItem('session_id', sessionId);
  }
  return sessionId;
}

// Call this on successful login to generate new session id
export function initializeSession() {
  const newSessionId = generateUUID();
  sessionStorage.setItem('session_id', newSessionId);
  return newSessionId;
}

// Always get access_token from localStorage for API calls
export function getAuthToken(): string | null {
  return localStorage.getItem('access_token');
}

export function getRefreshToken(): string | null {
  return localStorage.getItem('refresh_token');
}

export function setAuthToken(token: string) {
  localStorage.setItem('access_token', token);
}

export function setRefreshToken(token: string) {
  localStorage.setItem('refresh_token', token);
}

export function clearAuthToken() {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
}

// Centralized header builder for JSON APIs
export function getHeaders(
  contentType: string | null = 'application/json'
): Headers {
  const headers = new Headers();
  headers.append('x_interaction_id', generateUUID());
  headers.append('x_session_id', getSessionId());
  headers.append('x_device_id', getDeviceId());
  if (contentType) headers.append('Content-Type', contentType);

  const token = getAuthToken();
  if (token) headers.append('Authorization', `Bearer ${token}`);

  return headers;
}

// Export tryRefreshToken for use in App.tsx
export async function tryRefreshToken() {
  const refreshToken = getRefreshToken();
  if (!refreshToken) return false;
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: new Headers({ 'Content-Type': 'application/json' }),
      body: JSON.stringify({ refresh_token: refreshToken }),
    });
    if (!response.ok) return false;
    const data = await response.json();
    if (data.access_token) {
      setAuthToken(data.access_token);
      return true;
    }
    return false;
  } catch {
    return false;
  }
}

// Wrapper for API calls with token refresh logic
async function fetchWithAuthRetry(
  url: string,
  options: RequestInit,
  retry = true
): Promise<Response> {
  let token = getAuthToken();
  if (token && options.headers instanceof Headers) {
    options.headers.set('Authorization', `Bearer ${token}`);
  }
  let response = await fetch(url, options);
  if (response.status === 401 && retry) {
    const refreshed = await tryRefreshToken();
    if (refreshed) {
      token = getAuthToken();
      if (token && options.headers instanceof Headers) {
        options.headers.set('Authorization', `Bearer ${token}`);
      }
      response = await fetch(url, options);
    }
  }
  return response;
}

/**
 * Get insights for a chart with Analytics Tracking
 */
export const getChartInsights = async (
  chartId: string | number,
  chartData: any
): Promise<any> => {
  const analyticsInstance = AnalyticsService.getInstance();

  try {
    // Track insights request start
    await analyticsInstance.trackChartEvent(
      'CHART_INSIGHTS_REQUEST',
      chartData?.chart_type,
      chartId,
      {
        request_start: new Date().toISOString()
      }
    );

    const headers = getHeaders();
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/chart/executive-summary`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify({
          chart_id: chartId,
          chart_data: chartData,
        }),
      }
    );

    if (!response.ok) {
      // Track insights error
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_insights_failed',
        status_code: response.status,
        chart_id: chartId,
        chart_type: chartData?.chart_type
      });

      const apiError: ApiError = {
        message: response.status >= 500
          ? "Unable to generate insights at the moment. Please try again later."
          : "Unable to generate insights for this chart. Please try again.",
        type: response.status >= 500 ? 'error' : 'warning',
        statusCode: response.status
      };
      throw apiError;
    }

    const result = await response.json();

    // Track successful insights generation
    await analyticsInstance.trackChartEvent(
      'EXECUTIVE_SUMMARY_VIEW',
      chartData?.chart_type,
      chartId,
      {
        success: true,
        insights_count: result.insights?.length || 0,
        response_time: new Date().toISOString()
      }
    );

    return result;
  } catch (error) {
    console.error('Error getting chart insights:', error);

    // Track unexpected errors
    try {
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_insights_exception',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        chart_id: chartId,
        chart_type: chartData?.chart_type
      });
    } catch (analyticsError) {
      console.warn('Failed to track insights error:', analyticsError);
    }

    // If it's already an ApiError, re-throw it
    if (error && typeof error === 'object' && 'type' in error) {
      throw error;
    }

    // For other errors, create a technical error
    const apiError: ApiError = {
      message: "Unable to connect to our servers. Please check your internet connection and try again.",
      type: 'error',
      statusCode: 0
    };
    throw apiError;
  }
};

/**
 * Update chart data with Analytics Tracking
 */
export const updateChart = async (
  chartId: string | number,
  updatedData: any
): Promise<any> => {
  const analyticsInstance = AnalyticsService.getInstance();

  try {
    // Track chart update start
    await analyticsInstance.trackChartEvent(
      'CHART_UPDATE',
      updatedData?.chart_type,
      chartId,
      {
        update_start: new Date().toISOString(),
        fields_updated: Object.keys(updatedData || {})
      }
    );

    const headers = getHeaders();
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/chart/data`,
      {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          chart_id: chartId,
          updated_data: updatedData,
        }),
      }
    );

    if (!response.ok) {
      // Track update error
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_update_failed',
        status_code: response.status,
        chart_id: chartId,
        chart_type: updatedData?.chart_type
      });

      const apiError: ApiError = {
        message: response.status >= 500
          ? "Unable to update chart at the moment. Please try again later."
          : "Unable to update chart with the provided data. Please check your changes and try again.",
        type: response.status >= 500 ? 'error' : 'warning',
        statusCode: response.status
      };
      throw apiError;
    }

    const result = await response.json();

    // Track successful chart update
    await analyticsInstance.trackChartEvent(
      'CHART_UPDATE',
      updatedData?.chart_type,
      chartId,
      {
        success: true,
        update_completed: new Date().toISOString(),
        fields_updated: Object.keys(updatedData || {})
      }
    );

    return result;
  } catch (error) {
    console.error('Error updating chart:', error);

    // Track unexpected errors
    try {
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'chart_update_exception',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        chart_id: chartId,
        chart_type: updatedData?.chart_type
      });
    } catch (analyticsError) {
      console.warn('Failed to track update error:', analyticsError);
    }

    // If it's already an ApiError, re-throw it
    if (error && typeof error === 'object' && 'type' in error) {
      throw error;
    }

    // For other errors, create a technical error
    const apiError: ApiError = {
      message: "Unable to connect to our servers. Please check your internet connection and try again.",
      type: 'error',
      statusCode: 0
    };
    throw apiError;
  }
};

// Error types for different severity levels
export interface ApiError {
  message: string;
  type: 'warning' | 'error';
  statusCode?: number;
}

/**
 * Upload and process CSV data with Analytics Tracking
 */
export const uploadCSV = async (
  formData: FormData
): Promise<any> => {
  const deviceId = getDeviceId();
  const file = formData.get('file') as File;
  const analyticsInstance = AnalyticsService.getInstance();

  try {
    // Track file upload start
    if (file) {
      await analyticsInstance.trackFileUpload(
        file.name,
        file.size,
        file.type,
        {
          upload_start: new Date().toISOString(),
          device_id: deviceId
        }
      );
    }

    const headers = new Headers();
    headers.append('X-Interaction-Id', generateUUID());
    headers.append('X-Session-Id', getSessionId());
    headers.append('X-Device-Id', deviceId);
    // Do not set Content-Type for FormData
    const response = await fetchWithAuthRetry(
      `${API_BASE_URL}/data/file-upload`,
      {
        method: 'POST',
        headers,
        body: formData,
      }
    );

    // Always try to parse the response as JSON
    const responseData = await response.json();

    if (!response.ok) {
      // Track upload error
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'file_upload_failed',
        status_code: response.status,
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type
      });

      // Create structured error based on status code
      const apiError: ApiError = {
        message: '',
        type: response.status >= 500 ? 'error' : 'warning',
        statusCode: response.status
      };

      // Set appropriate message based on status code
      if (response.status >= 400 && response.status < 500) {
        // Business/client errors - user can retry
        apiError.message = responseData.message ||
          "No charts could be generated from your data. Please check your file format and try again.";
      } else if (response.status >= 500) {
        // Server errors - technical issues
        apiError.message = "Our server is unable to process your request at the moment. Please try again later!";
      } else {
        apiError.message = "An unexpected error occurred. Please try again.";
      }

      throw apiError;
    }

    // Check if the response contains an error field (even with 200 status)
    if (responseData.error) {
      // Track business logic error
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'file_processing_failed',
        error_message: responseData.error,
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type
      });

      const apiError: ApiError = {
        message: responseData.error,
        type: 'warning', // Treat response errors as business logic issues
        statusCode: 200
      };
      throw apiError;
    }

    // Track successful upload and chart generation
    if (responseData.charts && Array.isArray(responseData.charts)) {
      await analyticsInstance.trackEvent('FILE_UPLOAD', {
        success: true,
        charts_generated: responseData.charts.length,
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type,
        upload_duration: new Date().toISOString(),
        chart_types: responseData.charts.map((chart: any) => chart.chart_type)
      });
    }

    return responseData;
  } catch (error) {
    console.error('Error uploading CSV:', error);

    // Track unexpected errors
    try {
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'file_upload_exception',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        file_name: file?.name,
        file_size: file?.size,
        file_type: file?.type
      });
    } catch (analyticsError) {
      console.warn('Failed to track upload error:', analyticsError);
    }

    // If it's already an ApiError, re-throw it
    if (error && typeof error === 'object' && 'type' in error) {
      throw error;
    }

    // For network errors or other unexpected errors
    const apiError: ApiError = {
      message: "Unable to connect to our servers. Please check your internet connection and try again.",
      type: 'error',
      statusCode: 0
    };
    throw apiError;
  }
};

/**
 * Google OAuth Login with Analytics Tracking
 */
export const googleLogin = async (googleToken: string): Promise<any> => {
  try {
    // Enhanced login data with analytics information
    const loginData = {
      token: googleToken,
      user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      session_id: getSessionId(),
      device_id: getDeviceId(),
      // Add location if available from geolocation API
      timestamp: new Date().toISOString()
    };

    const response = await fetch(`${API_BASE_URL}/auth/google`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(loginData),
    });

    if (!response.ok) {
      throw new Error(`Google login failed: ${response.status}`);
    }
    const data = await response.json();

    // Save token after successful login
    if (data.access_token) {
      setAuthToken(data.access_token);
      initializeSession();

      // Update analytics session and track login event
      const analyticsInstance = AnalyticsService.getInstance();
      analyticsInstance.updateSessionId();

      // Track login event with user metadata
      await analyticsInstance.trackEvent('USER_LOGIN', {
        login_method: 'google',
        user_agent: loginData.user_agent,
        device_id: loginData.device_id,
        timestamp: loginData.timestamp
      });
    }

    return data;
  } catch (error) {
    console.error('Error during Google login:', error);

    // Track login error
    try {
      const analyticsInstance = AnalyticsService.getInstance();
      await analyticsInstance.trackEvent('ERROR_OCCURRED', {
        error_type: 'login_failed',
        error_message: error instanceof Error ? error.message : 'Unknown login error',
        login_method: 'google'
      });
    } catch (analyticsError) {
      console.warn('Failed to track login error:', analyticsError);
    }

    throw error;
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (refreshToken: string): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error refreshing token:', error);
    throw error;
  }
};

// ============================================================================
// ANALYTICS SERVICE
// ============================================================================

// Event types for analytics tracking
export type AnalyticsEventType =
  | 'USER_LOGIN'
  | 'FILE_UPLOAD'
  | 'CHART_SAVE'
  | 'CHART_DOWNLOAD'
  | 'CHART_VIEW'
  | 'ADDITIONAL_CHARTS_VIEW'
  | 'EXECUTIVE_SUMMARY_VIEW'
  | 'CHART_UPDATE'
  | 'PAGE_VIEW'
  | 'CHART_COPY'
  | 'CHART_INSIGHTS_REQUEST'
  | 'NAVIGATION'
  | 'ERROR_OCCURRED';

// Analytics interfaces
export interface AnalyticsEvent {
  event_type: AnalyticsEventType;
  metadata?: Record<string, any>;
  session_id?: string;
  page_url?: string;
  user_agent?: string;
  timestamp?: string;
}

export interface FileUploadEvent {
  file_name: string;
  file_size: number;
  file_type: string;
  session_id?: string;
  metadata?: Record<string, any>;
}

export interface ChartEvent {
  event_type: AnalyticsEventType;
  chart_type?: string;
  chart_id?: string | number;
  session_id?: string;
  metadata?: Record<string, any>;
}

export interface PageViewEvent {
  page_title: string;
  page_url: string;
  session_id?: string;
  metadata?: Record<string, any>;
}

/**
 * Analytics Service Class
 * Handles all analytics tracking with automatic retry and error handling
 */
export class AnalyticsService {
  private static instance: AnalyticsService | null = null;
  private sessionId: string;
  private isEnabled: boolean = true;
  private retryAttempts: number = 3;
  private retryDelay: number = 1000; // 1 second

  constructor() {
    this.sessionId = getSessionId();
  }

  // Singleton pattern to ensure one instance across the app
  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Enable/disable analytics tracking
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Update session ID (call this after login)
  public updateSessionId(): void {
    this.sessionId = getSessionId();
  }

  // Configure retry settings
  public setRetryConfig(attempts: number, delay: number): void {
    this.retryAttempts = Math.max(1, attempts);
    this.retryDelay = Math.max(100, delay);
  }

  // Validate event data before sending
  private validateEventData(eventType: AnalyticsEventType, metadata?: Record<string, any>): boolean {
    if (!eventType || typeof eventType !== 'string') {
      console.warn('Analytics: Invalid event type provided');
      return false;
    }

    if (metadata && typeof metadata !== 'object') {
      console.warn('Analytics: Invalid metadata provided');
      return false;
    }

    return true;
  }

  // Retry wrapper for analytics requests
  private async retryRequest(requestFn: () => Promise<Response>, context: string): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await requestFn();

        if (response.ok) {
          return; // Success
        }

        // Don't retry client errors (4xx), only server errors (5xx) and network issues
        if (response.status >= 400 && response.status < 500) {
          console.warn(`Analytics ${context}: Client error ${response.status}, not retrying`);
          return;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt < this.retryAttempts) {
          console.warn(`Analytics ${context}: Attempt ${attempt} failed, retrying in ${this.retryDelay}ms...`, lastError.message);
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        }
      }
    }

    console.warn(`Analytics ${context}: All ${this.retryAttempts} attempts failed`, lastError?.message);
  }

  /**
   * Track general events
   */
  public async trackEvent(
    eventType: AnalyticsEventType,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.isEnabled) return;

    if (!this.validateEventData(eventType, metadata)) {
      return;
    }

    try {
      const headers = getHeaders();
      const eventData: AnalyticsEvent = {
        event_type: eventType,
        metadata: {
          ...metadata,
          // Ensure metadata doesn't contain circular references
          timestamp: new Date().toISOString()
        },
        session_id: this.sessionId,
        page_url: typeof window !== 'undefined' ? window.location.href : '',
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
        timestamp: new Date().toISOString()
      };

      await this.retryRequest(
        () => fetchWithAuthRetry(
          `${API_BASE_URL}/analytics/events`,
          {
            method: 'POST',
            headers,
            body: JSON.stringify(eventData),
          }
        ),
        `general event: ${eventType}`
      );
    } catch (error) {
      console.warn('Analytics tracking failed for event:', eventType, error);
      // Don't throw error to avoid disrupting user experience
    }
  }

  /**
   * Track file upload events
   */
  public async trackFileUpload(
    fileName: string,
    fileSize: number,
    fileType: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.isEnabled) return;

    // Validate required parameters
    if (!fileName || typeof fileName !== 'string') {
      console.warn('Analytics: Invalid file name provided for upload tracking');
      return;
    }

    if (typeof fileSize !== 'number' || fileSize < 0) {
      console.warn('Analytics: Invalid file size provided for upload tracking');
      return;
    }

    try {
      const headers = getHeaders();
      const uploadData: FileUploadEvent = {
        file_name: fileName,
        file_size: fileSize,
        file_type: fileType || 'unknown',
        session_id: this.sessionId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          page_url: typeof window !== 'undefined' ? window.location.href : ''
        }
      };

      await this.retryRequest(
        () => fetchWithAuthRetry(
          `${API_BASE_URL}/analytics/events/file-upload`,
          {
            method: 'POST',
            headers,
            body: JSON.stringify(uploadData),
          }
        ),
        `file upload: ${fileName}`
      );
    } catch (error) {
      console.warn('Analytics tracking failed for file upload:', error);
    }
  }

  /**
   * Track chart-related events
   */
  public async trackChartEvent(
    eventType: AnalyticsEventType,
    chartType?: string,
    chartId?: string | number,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.isEnabled) return;

    if (!this.validateEventData(eventType, metadata)) {
      return;
    }

    try {
      const headers = getHeaders();
      const chartData: ChartEvent = {
        event_type: eventType,
        chart_type: chartType || 'unknown',
        chart_id: chartId,
        session_id: this.sessionId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          page_url: typeof window !== 'undefined' ? window.location.href : ''
        }
      };

      await this.retryRequest(
        () => fetchWithAuthRetry(
          `${API_BASE_URL}/analytics/events/chart`,
          {
            method: 'POST',
            headers,
            body: JSON.stringify(chartData),
          }
        ),
        `chart event: ${eventType} (${chartType})`
      );
    } catch (error) {
      console.warn('Analytics tracking failed for chart event:', eventType, error);
    }
  }

  /**
   * Track page view events
   */
  public async trackPageView(
    pageTitle?: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const headers = getHeaders();
      const pageUrl = typeof window !== 'undefined' ? window.location.href : '';
      const title = pageTitle || (typeof document !== 'undefined' ? document.title : '');

      // Validate page URL
      if (!pageUrl) {
        console.warn('Analytics: No page URL available for page view tracking');
        return;
      }

      // Create URL with query parameters as expected by backend
      const url = new URL(`${API_BASE_URL}/analytics/events/page-view`);
      url.searchParams.append('page_url', pageUrl);

      const pageData: PageViewEvent = {
        page_title: title || 'Untitled Page',
        page_url: pageUrl,
        session_id: this.sessionId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          referrer: typeof document !== 'undefined' ? document.referrer : '',
          user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : ''
        }
      };

      await this.retryRequest(
        () => fetchWithAuthRetry(
          url.toString(),
          {
            method: 'POST',
            headers,
            body: JSON.stringify(pageData),
          }
        ),
        `page view: ${title}`
      );
    } catch (error) {
      console.warn('Analytics tracking failed for page view:', error);
    }
  }

  /**
   * Get user profile analytics
   */
  public async getUserProfile(): Promise<any> {
    try {
      const headers = getHeaders();
      const response = await fetchWithAuthRetry(
        `${API_BASE_URL}/analytics/user-profile`,
        {
          method: 'GET',
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to get user profile: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  /**
   * Check analytics service health
   */
  public async checkHealth(): Promise<any> {
    try {
      const headers = getHeaders();
      const response = await fetchWithAuthRetry(
        `${API_BASE_URL}=analytics/health`,
        {
          method: 'GET',
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`Analytics health check failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Analytics health check failed:', error);
      throw error;
    }
  }
}

// Export singleton instance for easy access
export const analytics = AnalyticsService.getInstance();
