import React from 'react';
import { Box, Typography, Link as MuiLink } from '@mui/material';
import DatasetIcon from '@mui/icons-material/Dataset';
import BarChartIcon from '@mui/icons-material/BarChart';

export type PanelType = 'data' | 'charts';

interface LeftPanelNavProps {
  currentPanel: PanelType;
  onPanelChange: (panel: PanelType) => void;
}

const LeftPanelNav: React.FC<LeftPanelNavProps> = ({ currentPanel, onPanelChange }) => {
  return (
    <Box sx={{
      width: '250px',
      minHeight: '100vh',
      backgroundColor: 'white',
      borderRight: '1px solid rgba(0, 0, 0, 0.12)',
      boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
      gap: 2,
    }}>


      {/* Navigation Links */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <MuiLink
          href="#"
          underline="none"
          onClick={() => onPanelChange('data')}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            p: 2,
            borderRadius: 2,
            color: currentPanel === 'data' ? 'primary.main' : 'text.primary',
            backgroundColor: currentPanel === 'data' ? 'primary.50' : 'transparent',
            fontWeight: currentPanel === 'data' ? 'bold' : 'normal',
            fontSize: '1rem',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: currentPanel === 'data' ? 'primary.100' : 'grey.50',
              transform: 'translateX(4px)',
            },
          }}
        >
          <DatasetIcon sx={{ fontSize: 24 }} />
          <Typography variant="body1" sx={{ fontWeight: 'inherit' }}>
            Bring Your Data
          </Typography>
        </MuiLink>

        <MuiLink
          href="#"
          underline="none"
          onClick={() => onPanelChange('charts')}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            p: 2,
            borderRadius: 2,
            color: currentPanel === 'charts' ? 'primary.main' : 'text.primary',
            backgroundColor: currentPanel === 'charts' ? 'primary.50' : 'transparent',
            fontWeight: currentPanel === 'charts' ? 'bold' : 'normal',
            fontSize: '1rem',
            transition: 'all 0.2s ease',
            '&:hover': {
              backgroundColor: currentPanel === 'charts' ? 'primary.100' : 'grey.50',
              transform: 'translateX(4px)',
            },
          }}
        >
          <BarChartIcon sx={{ fontSize: 24 }} />
          <Typography variant="body1" sx={{ fontWeight: 'inherit' }}>
            View Your Charts
          </Typography>
        </MuiLink>
      </Box>
    </Box>
  );
};

export default LeftPanelNav;