import React from "react";
import { useDropzone } from "react-dropzone";
import { Box, Paper, Typography } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { uploadCSV, ApiError } from '../services/apiService';

interface FileUploadProps {
  onDataProcessed: (data: any[]) => void;
  onError?: (error: ApiError) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onDataProcessed, onError }) => {

  const [file, setFile] = React.useState<File | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    multiple: false,
    onDrop: async (acceptedFiles) => {
      const selectedFile = acceptedFiles[0];
      setFile(selectedFile);
      if (selectedFile) {
        setIsLoading(true);
        await handleUpload(selectedFile);
        setIsLoading(false);
      }
    }
  });

  const handleUpload = async (uploadFile: File | null = null) => {
    const fileToUpload = uploadFile || file;
    if (!fileToUpload) return;
    const formData = new FormData();
    formData.append('file', fileToUpload);
    try {
      const data = await uploadCSV(formData);

      // Check if the response contains an error
      if (data.error) {
        const apiError: ApiError = {
          message: "Our server is unable to process your request at the moment. Please try again later!",
          type: 'error'
        };
        onError?.(apiError);
        return;
      }

      // Check if charts data is valid
      if (data && Array.isArray(data.charts) && data.charts.length > 0) {
        const processedCharts = data.charts.map((chart: any) => {
          return {
            chart_type: chart.chart_type,
            chart_group: chart.chart_group, // Include chart_group field
            library: chart.library,
            data: chart.data,
            layout: chart.layout
          };
        });
        onDataProcessed(processedCharts);
        setFile(null);
      } else {
        // Handle case where charts array is empty or invalid - this is a business logic issue
        const apiError: ApiError = {
          message: "No charts could be generated from your data. Please check your file format and try again.",
          type: 'warning'
        };
        onError?.(apiError);
      }
    } catch (error: any) {
      console.error('Error uploading file:', error);

      // Check if it's already an ApiError from the service
      if (error && typeof error === 'object' && 'type' in error && 'message' in error) {
        onError?.(error as ApiError);
        return;
      }

      // For other unexpected errors, create a technical error
      const apiError: ApiError = {
        message: "Unable to connect to our servers. Please check your internet connection and try again.",
        type: 'error'
      };
      onError?.(apiError);
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      <Paper
        {...getRootProps()}
        sx={{
          width: '100%',
          height: '100%',
          minHeight: '300px',
          p: 4,
          border: '3px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.400',
          borderRadius: '16px',
          backgroundColor: isDragActive ? 'primary.50' : 'white',
          cursor: isLoading ? 'default' : 'pointer',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            borderColor: isLoading ? 'grey.400' : 'primary.main',
            backgroundColor: isLoading ? 'white' : 'primary.50',
          },
          opacity: isLoading ? 0.7 : 1,
          pointerEvents: isLoading ? 'none' : 'auto',
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: 'none', // Remove default shadow since parent has it
        }}
      >
        <input {...getInputProps()} disabled={isLoading} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 3,
            textAlign: 'center',
          }}
        >
          <CloudUploadIcon
            sx={{
              fontSize: 80,
              color: isDragActive ? 'primary.main' : 'grey.500',
              transition: 'all 0.3s ease-in-out',
            }}
          />
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="h6" align="center" sx={{ fontWeight: 600 }}>
              {isLoading
                ? "Processing file..."
                : isDragActive
                  ? "Drop your file here"
                  : "Upload your data file"}
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center">
              {isLoading
                ? "Please wait while we process your data..."
                : "Drag & drop CSV or Excel files, or click to select"}
            </Typography>
            <Typography variant="caption" color="text.secondary" align="center">
              Supported formats: .csv, .xlsx, .xls
            </Typography>
            {file && !isLoading && (
              <Typography variant="body2" color="primary.main" sx={{ mt: 1, fontWeight: 500 }}>
                Selected: {file.name}
              </Typography>
            )}
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default FileUpload;
