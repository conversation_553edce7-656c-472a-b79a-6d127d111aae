import React, { useState } from "react";
import { Box, Paper, Typography, List, ListItem, ListItemIcon, ListItemText, IconButton } from '@mui/material';
import CircleIcon from '@mui/icons-material/Circle';
import CloseIcon from '@mui/icons-material/Close';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Chart from './Chart';
import { getChartInsights, updateChart } from '../services/apiService';
import ProfessionalButton from './common/ProfessionalButton';
import useAnalytics from '../hooks/useAnalytics';

// Export ChartData interface for use in Dashboard and other components
export interface ChartData {
  id: string | number; // Expect an ID for the key
  chart_type: string;
  chart_group?: string; // New field for grouping charts
  library: string;
  data: any;
  layout: {
    title?: string | { text: string };
    xaxis?: { title?: string | { text: string } };
    yaxis?: { title?: string | { text: string }; side?: 'left' | 'right' };
  };
  fields?: {
    x?: string;
    y?: string;
    numeric?: string;
    agg?: string;
  };
  available_fields?: {
    categorical?: string[];
    numerical?: string[];
    datetime?: string[];
  };
}

interface ChartGridProps {
  charts: ChartData[];
  availableColumns: string[]; // Keep if needed for Chart component edits
  onChartUpdate?: (index: number, updatedChart: any) => void; // Callback to update state in Dashboard
  gridBackgroundColor?: string;
  onGroupClick?: (groupName: string, charts: ChartData[]) => void; // Callback to navigate to group page
}

const ChartGrid: React.FC<ChartGridProps> = ({
  charts,
  onChartUpdate,
  onGroupClick
}) => {
  const [insightsData, setInsightsData] = useState<{ chartId: string | number, insights: string[], position: 'left' | 'center' | 'right' } | null>(null);

  // Analytics hook
  const { trackAdditionalChartsView } = useAnalytics();
  const handleChartUpdate = async (chartIndex: number, updatedChart: any) => {
    if (!onChartUpdate) return;

    try {
      const updatedChartData = await updateChart(charts[chartIndex].id, updatedChart);
      onChartUpdate(chartIndex, updatedChartData);
    } catch (error) {
      console.error('Error updating chart:', error);
    }
  };

  const handleInsightsRequest = async (chartId: string | number, chartData: any, position: 'left' | 'center' | 'right') => {
    try {
      // Call the API service to get insights
      const insights = await getChartInsights(chartId, chartData);

      console.log('Chart insights received:', insights);

      // Update state to display insights
      setInsightsData({
        chartId,
        insights: insights.insights,
        position
      });

      return insights;
    } catch (error) {
      console.error('Error getting chart insights:', error);
      return null;
    }
  };

  // Group charts by chart_group
  const groupedCharts = charts.reduce((groups, chart) => {
    const group = chart.chart_group || 'ungrouped';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(chart);
    return groups;
  }, {} as Record<string, ChartData[]>);

  // Get the first chart from each group for display
  const displayCharts = Object.entries(groupedCharts).map(([groupName, groupCharts]) => ({
    groupName,
    chart: groupCharts[0], // First chart from the group
    totalCharts: groupCharts.length,
    allCharts: groupCharts
  }));

  // Group display charts into rows of 2
  const chartRows = [];
  for (let i = 0; i < displayCharts.length; i += 2) {
    chartRows.push(displayCharts.slice(i, i + 2));
  }

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 2, // Reduced gap between rows to show more content
      }}
    >
      {/* Process charts in rows of two */}
      {chartRows.map((rowCharts, rowIndex) => {
        return (
          <Box
            key={`row-${rowIndex}`}
            sx={{
              display: 'flex',
              width: '100%',
              gap: 2, // Reduced gap between charts in a row
              justifyContent: 'flex-start',
              flexWrap: 'wrap',
            }}
          >
            {/* Render each chart group in the row */}
            {rowCharts.map((chartGroup, colIndex) => {
              const chartIndex = rowIndex * 2 + colIndex;
              const chartHasInsights = insightsData && insightsData.chartId === chartGroup.chart?.id;

              // If this chart has insights, show them in a modal or overlay
              if (chartHasInsights) {
                return (
                  <Paper
                    key={`chart-${chartIndex}`}
                    elevation={0}
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      borderRadius: 2,
                      padding: 3,
                      width: 'calc(50% - 12px)', // Adjusted width for 2 charts per row
                      aspectRatio: '1.5/1',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      overflow: 'auto',
                      boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">
                        Chart Insights
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => setInsightsData(null)}
                        aria-label="close insights"
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </Box>

                    <List dense>
                      {insightsData.insights.map((insight, i) => (
                        <ListItem key={i} alignItems="flex-start">
                          <ListItemIcon sx={{ minWidth: '24px' }}>
                            <CircleIcon sx={{ fontSize: 8 }} />
                          </ListItemIcon>
                          <ListItemText primary={insight} />
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                );
              }

              // Show chart normally with "More Charts" button if there are multiple charts in the group
              return chartGroup && (
                <Paper
                  key={`chart-${chartIndex}`}
                  elevation={0}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    borderRadius: 2,
                    overflow: 'hidden',
                    width: 'calc(50% - 12px)', // Adjusted width for 2 charts per row
                    aspectRatio: '1.5/1',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
                  }}
                >
                  <Chart
                    data={chartGroup.chart}
                    position={colIndex === 0 ? "left" : "right"}
                    onChartUpdate={(updatedChart) => handleChartUpdate(chartIndex, updatedChart)}
                    onInsightsRequest={handleInsightsRequest}
                  />

                  {/* More Charts Button - Moved to bottom right corner */}
                  {chartGroup.totalCharts > 1 && (
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 10,
                        right: 10,
                        zIndex: 3,
                      }}
                    >
                      <ProfessionalButton
                        variant="primary"
                        size="small"
                        startIcon={<MoreHorizIcon />}
                        onClick={async () => {
                          // Track additional charts view
                          await trackAdditionalChartsView(
                            chartGroup.groupName,
                            chartGroup.totalCharts,
                            {
                              main_chart_type: chartGroup.chart.chart_type,
                              main_chart_id: chartGroup.chart.id
                            }
                          );
                          onGroupClick?.(chartGroup.groupName, chartGroup.allCharts);
                        }}
                        sx={{
                          fontSize: '0.75rem',
                          padding: '4px 8px',
                          minWidth: 'auto',
                        }}
                      >
                        +{chartGroup.totalCharts - 1}
                      </ProfessionalButton>
                    </Box>
                  )}
                </Paper>
              );
            })}
          </Box>
        );
      })}
    </Box>
  );
};

export default ChartGrid;
