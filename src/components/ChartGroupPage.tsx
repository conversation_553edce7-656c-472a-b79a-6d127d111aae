import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Chart from './Chart';
import { ChartData } from './ChartGrid';
import ProfessionalButton from './common/ProfessionalButton';

interface ChartGroupPageProps {
  groupName: string;
  charts: ChartData[];
  onBack: () => void;
  onChartUpdate: (index: number, updatedChart: ChartData) => void;
  gridBackgroundColor: string;
}

const ChartGroupPage: React.FC<ChartGroupPageProps> = ({
  charts,
  onBack,
}) => {
  // Group charts into rows of 2
  const chartRows = [];
  for (let i = 0; i < charts.length; i += 2) {
    chartRows.push(charts.slice(i, i + 2));
  }

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
      backgroundColor: 'white',
      minHeight: '100vh',
    }}>
      {/* <PERSON> Header */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
      }}>
        <Box>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              color: 'text.primary',
              mb: 1,
            }}
          >
            Additional Charts
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: 'text.secondary',
            }}
          >
            {charts.length} similar chart{charts.length !== 1 ? 's' : ''} generated
          </Typography>
        </Box>

        <ProfessionalButton
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={onBack}
        >
          Back
        </ProfessionalButton>
      </Box>

      {/* Charts Section */}
      <Box sx={{
        width: '100%',
        backgroundColor: 'white',
        p: 3,
        flexGrow: 1,
        overflow: 'auto',
      }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
        }}>
          {chartRows.map((rowCharts, rowIndex) => (
            <Box
              key={`row-${rowIndex}`}
              sx={{
                display: 'flex',
                width: '100%',
                gap: 2,
                justifyContent: 'flex-start',
                flexWrap: 'wrap',
              }}
            >
              {rowCharts.map((chart, colIndex) => {
                const chartIndex = rowIndex * 2 + colIndex;

                return chart && (
                  <Paper
                    key={`chart-${chartIndex}`}
                    elevation={0}
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                      borderRadius: 2,
                      overflow: 'hidden',
                      width: 'calc(50% - 12px)',
                      aspectRatio: '1.5/1',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
                    }}
                  >
                    <Chart
                      data={chart}
                      position={colIndex === 0 ? "left" : "right"}
                    />
                  </Paper>
                );
              })}
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default ChartGroupPage;
