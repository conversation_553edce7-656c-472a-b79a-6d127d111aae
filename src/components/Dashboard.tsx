import React, { useState } from 'react';
import { <PERSON>, Snackbar, Alert } from '@mui/material';
import LeftPanelNav, { PanelType } from './LeftPanelNav';
import DataPage from './DataPage';
import ChartsPage from './ChartsPage';
import useChartData from '../hooks/useChartData';

const Dashboard: React.FC<{ onNavigate: (page: string) => void }> = ({ }) => {
  const {
    charts,
    addChartData,
    updateChartData,
    resetChartData,
  } = useChartData();

  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [isClearing, setIsClearing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingStep, setLoadingStep] = useState(0);
  const [showSteps, setShowSteps] = useState(false);
  const [currentPanel, setCurrentPanel] = useState<PanelType>('data');

  const clearError = () => setError(null);

  // Helper to animate loading steps
  const runLoadingSteps = async () => {
    setShowSteps(true);
    const LOADING_STEPS = [
      'Reading Data',
      'Identifying Data Points',
      'Aggregating Data',
      'Identifying Suitable Charts',
      'Preparing Charts'
    ];
    for (let i = 0; i < LOADING_STEPS.length; i++) {
      setLoadingStep(i);
      await new Promise(resolve => setTimeout(resolve, 600));
    }
  };

  const handleDataProcessed = async (responseData: { charts: any[], available_columns: string[] }) => {
    setIsClearing(true);
    setLoadingStep(0);
    await runLoadingSteps();
    resetChartData();
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      if (responseData && Array.isArray(responseData.charts) && responseData.charts.length > 0) {
        const chartsWithIds = responseData.charts.map((chart, index) => ({
          ...chart,
          id: chart.id || `chart-${Date.now()}-${index}`,
        }));
        addChartData(chartsWithIds);
        setAvailableColumns(responseData.available_columns || []);
        // Auto-switch to charts page after successful data processing
        setCurrentPanel('charts');
      } else {
        throw new Error("Invalid or empty chart data received");
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while processing data.');
    }

    setIsClearing(false);
    setShowSteps(false); // Hide steps after processing
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setIsClearing(false);
    setShowSteps(false);
  };

  const handleChartUpdate = (id: string | number, updatedChartData: any) => {
    updateChartData(Number(id), updatedChartData);
  };

  const handlePanelChange = (panel: PanelType) => {
    setCurrentPanel(panel);
  };

  const handleSwitchToDataPage = () => {
    setCurrentPanel('data');
  };

  // Render main dashboard
  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'row',
      width: '100%',
      minHeight: '100vh',
      backgroundColor: 'white',
    }}>
      {/* Left Panel Navigation */}
      <LeftPanelNav
        currentPanel={currentPanel}
        onPanelChange={handlePanelChange}
      />

      {/* Main Content Area */}
      <Box sx={{
        flexGrow: 1,
        backgroundColor: 'white',
        overflow: 'auto',
        minHeight: '100vh',
      }}>
        {currentPanel === 'data' ? (
          <DataPage
            onDataProcessed={handleDataProcessed}
            onError={handleError}
            isLoading={isClearing}
            loadingStep={loadingStep}
            showSteps={showSteps}
          />
        ) : (
          <ChartsPage
            charts={charts}
            availableColumns={availableColumns}
            onChartUpdate={handleChartUpdate}
            onSwitchToDataPage={handleSwitchToDataPage}
          />
        )}
      </Box>

      {/* Snackbar for errors */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={clearError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ mt: 2 }}
      >
        <Alert
          severity="error"
          onClose={clearError}
          variant="filled"
          sx={{
            fontSize: '1rem',
            fontWeight: 500,
            '& .MuiAlert-message': {
              display: 'flex',
              alignItems: 'center',
            }
          }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Dashboard;
