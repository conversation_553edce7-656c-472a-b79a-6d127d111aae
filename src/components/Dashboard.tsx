import React, { useState } from 'react';
import { Box, Snackbar, Alert } from '@mui/material';
import LeftPanelNav, { PanelType } from './LeftPanelNav';
import DataPage from './DataPage';
import ChartsPage from './ChartsPage';
import useChartData from '../hooks/useChartData';
import { ApiError } from '../services/apiService';
import useAnalytics from '../hooks/useAnalytics';

const Dashboard: React.FC<{ onNavigate: (page: string) => void }> = ({ }) => {
  const {
    charts,
    addChartData,
    updateChartData,
    resetChartData,
  } = useChartData();

  const [availableColumns, setAvailableColumns] = useState<string[]>([]);
  const [isClearing, setIsClearing] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);
  const [loadingStep, setLoadingStep] = useState(0);
  const [showSteps, setShowSteps] = useState(false);
  const [currentPanel, setCurrentPanel] = useState<PanelType>('data');

  // Analytics hook
  const { trackNavigation, trackEvent } = useAnalytics();

  const clearError = () => setError(null);

  // Helper to animate loading steps
  const runLoadingSteps = async () => {
    setShowSteps(true);
    const LOADING_STEPS = [
      'Reading Data',
      'Identifying Data Points',
      'Aggregating Data',
      'Identifying Suitable Charts',
      'Preparing Charts'
    ];
    for (let i = 0; i < LOADING_STEPS.length; i++) {
      setLoadingStep(i);
      await new Promise(resolve => setTimeout(resolve, 600));
    }
  };

  const handleDataProcessed = async (responseData: { charts: any[], available_columns: string[] }) => {
    setIsClearing(true);
    setLoadingStep(0);
    await runLoadingSteps();
    resetChartData();
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      if (responseData && Array.isArray(responseData.charts) && responseData.charts.length > 0) {
        const chartsWithIds = responseData.charts.map((chart, index) => ({
          ...chart,
          id: chart.id || `chart-${Date.now()}-${index}`,
        }));
        addChartData(chartsWithIds);
        setAvailableColumns(responseData.available_columns || []);

        // Track successful chart generation
        await trackEvent('FILE_UPLOAD', {
          success: true,
          charts_generated: chartsWithIds.length,
          chart_types: chartsWithIds.map(chart => chart.chart_type),
          available_columns: responseData.available_columns?.length || 0,
          processing_completed: new Date().toISOString()
        });

        // Auto-switch to charts page after successful data processing
        const fromPanel = currentPanel;
        setCurrentPanel('charts');

        // Track navigation to charts page
        await trackNavigation(fromPanel, 'charts', {
          charts_count: chartsWithIds.length,
          auto_navigation: true,
          reason: 'successful_upload'
        });
      } else {
        throw new Error("Invalid or empty chart data received");
      }
    } catch (err: any) {
      // Track processing error
      await trackEvent('ERROR_OCCURRED', {
        error_type: 'data_processing_failed',
        error_message: err.message || 'Unknown error',
        stage: 'post_upload_processing'
      });

      const apiError: ApiError = {
        message: err.message || 'An error occurred while processing data.',
        type: 'error'
      };
      setError(apiError);
    }

    setIsClearing(false);
    setShowSteps(false); // Hide steps after processing
  };

  const handleError = (error: ApiError) => {
    setError(error);
    setIsClearing(false);
    setShowSteps(false);
  };

  const handleChartUpdate = (id: string | number, updatedChartData: any) => {
    updateChartData(Number(id), updatedChartData);
  };

  const handlePanelChange = async (panel: PanelType) => {
    const fromPanel = currentPanel;
    setCurrentPanel(panel);

    // Track navigation
    await trackNavigation(fromPanel, panel, {
      charts_count: charts.length,
      has_data: charts.length > 0
    });
  };

  const handleSwitchToDataPage = async () => {
    const fromPanel = currentPanel;
    setCurrentPanel('data');

    // Track navigation back to data page
    await trackNavigation(fromPanel, 'data', {
      charts_count: charts.length,
      reason: 'upload_new_data'
    });
  };

  // Render main dashboard
  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'row',
      width: '100%',
      minHeight: '100vh',
      backgroundColor: 'white',
    }}>
      {/* Left Panel Navigation */}
      <LeftPanelNav
        currentPanel={currentPanel}
        onPanelChange={handlePanelChange}
      />

      {/* Main Content Area */}
      <Box sx={{
        flexGrow: 1,
        backgroundColor: 'white',
        overflow: 'auto',
        minHeight: '100vh',
      }}>
        {currentPanel === 'data' ? (
          <DataPage
            onDataProcessed={handleDataProcessed}
            onError={handleError}
            isLoading={isClearing}
            loadingStep={loadingStep}
            showSteps={showSteps}
          />
        ) : (
          <ChartsPage
            charts={charts}
            availableColumns={availableColumns}
            onChartUpdate={handleChartUpdate}
            onSwitchToDataPage={handleSwitchToDataPage}
          />
        )}
      </Box>

      {/* Snackbar for errors */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={clearError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ mt: 2 }}
      >
        <Alert
          severity={error?.type === 'warning' ? 'warning' : 'error'}
          onClose={clearError}
          variant="filled"
          sx={{
            fontSize: '1rem',
            fontWeight: 500,
            backgroundColor: error?.type === 'warning' ? '#ff9800' : '#f44336',
            '& .MuiAlert-message': {
              display: 'flex',
              alignItems: 'center',
            },
            '& .MuiAlert-icon': {
              color: 'white',
            }
          }}
        >
          {error?.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Dashboard;
