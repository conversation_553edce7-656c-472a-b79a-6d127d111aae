import React from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import FileUpload from './FileUpload';

// Step-by-step loading states for a more engaging experience
const LOADING_STEPS = [
  'Reading Data',
  'Identifying Data Points',
  'Aggregating Data',
  'Identifying Suitable Charts',
  'Preparing Charts'
];

interface DataPageProps {
  onDataProcessed: (data: { charts: any[], available_columns: string[] }) => void;
  onError?: (error: string) => void;
  isLoading: boolean;
  loadingStep: number;
  showSteps: boolean;
}

const DataPage: React.FC<DataPageProps> = ({
  onDataProcessed,
  onError,
  isLoading,
  loadingStep,
  showSteps
}) => {
  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',

      p: 3,
      minHeight: '100vh',
      backgroundColor: 'white',
    }}>
      {/* Page Title */}
      <Typography
        variant="h4"
        sx={{
          mb: 2,
          fontWeight: 'bold',
          color: 'text.primary'
        }}
      >
        Your Data
      </Typography>

    

      {/* Loading Section */}
      {isLoading ? (
        <Box sx={{
          width: '100%',
          maxWidth: '500px',
          minHeight: '400px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
          borderRadius: '16px',
          p: 4,
          boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          border: '1px solid rgba(0,0,0,0.1)',
          transition: 'transform 0.4s ease, box-shadow 0.4s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
          },
        }}>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" sx={{ mb: 3, textAlign: 'center' }}>
            Processing your data...
          </Typography>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
            width: '100%',
          }}>
            {showSteps && LOADING_STEPS.map((step, idx) => (
              <Typography
                key={step}
                variant="body1"
                color={idx === loadingStep ? 'primary.main' : 'text.secondary'}
                sx={{
                  fontWeight: idx === loadingStep ? 600 : 400,
                  opacity: idx <= loadingStep ? 1 : 0.5,
                  transition: 'color 0.3s, opacity 0.3s',
                  textAlign: 'center',
                }}
              >
                {idx === loadingStep && '⏳ '}{step}
                {idx < loadingStep && ' ✓'}
              </Typography>
            ))}
          </Box>
        </Box>
      ) : (
        /* File Upload Section */
        <Box sx={{
          width: '100%',
          maxWidth: '500px',
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
          borderRadius: '16px',
          transition: 'transform 0.4s ease, box-shadow 0.4s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
          },
        }}>
          <FileUpload
            onDataProcessed={(data: any[]) => onDataProcessed({ charts: data, available_columns: [] })}
            onError={onError}
          />
        </Box>
      )}
    </Box>
  );
};

export default DataPage;
