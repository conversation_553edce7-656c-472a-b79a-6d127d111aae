import React, { useState, useEffect, useRef, useCallback } from "react";
import { toPng } from 'html-to-image';
import Plot from 'react-plotly.js';
import { Box, TextField, Typography, ClickAwayListener, Tooltip, Snackbar, Alert } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import useAnalytics from '../hooks/useAnalytics';

// import InsightsIcon from '@mui/icons-material/Lightbulb';
// import { getChartInsights } from '../services/apiService';

// Interface matching the expected structure from ChartGrid
interface ChartData {
  id: string | number;
  chart_type: string;
  chart_group?: string;
  library: string;
  fields?: {
    x?: string;
    x_type?: string; // Keep if used
    y?: string;
    y_type?: string; // Keep if used
    numeric?: string;
    agg?: string;
  };
  data: { // This structure might vary greatly
    x?: any[];
    y?: any[];
    z?: any[]; // For heatmap/3D
    type?: string; // e.g., 'bar', 'scatter', 'pie'
    labels?: string[]; // For pie
    values?: number[]; // For pie
    parents?: string[]; // For hierarchy charts (treemap, sunburst, icicle)
    mode?: string; // e.g., 'lines', 'markers'
    name?: string; // Legend entry name
    // Allow array of traces for multi-trace charts
  } | Array<{
    x?: any[];
    y?: any[];
    z?: any[];
    type?: string;
    labels?: string[];
    values?: number[];
    parents?: string[]; // For hierarchy charts (treemap, sunburst, icicle)
    mode?: string;
    name?: string;
  }>;
  layout: {
    title?: string | { text: string };
    xaxis?: { title?: string | { text: string }; [key: string]: any }; // Allow other xaxis props
    yaxis?: { title?: string | { text: string }; side?: 'left' | 'right'; [key: string]: any }; // Allow other yaxis props
    [key: string]: any; // Allow other layout properties like legend, annotations etc.
  };
  available_fields?: {
    categorical?: string[];
    numerical?: string[];
    datetime?: string[];
  };
}

interface ChartProps {
  data: ChartData;
  // availableColumns?: string[]; // Receive available columns if needed for edits
  onChartUpdate?: (updatedFields: any) => Promise<void>; // Expecting a promise indicates async operation
  onInsightsRequest?: (chartId: string | number, chartData: any, position: 'left' | 'center' | 'right') => Promise<any>; // Optional callback for insights
  position?: 'left' | 'center' | 'right'; // Position of the chart in the grid
}

interface EditableTextProps {
  value: string;
  onChange: (newValue: string) => void;
  variant?: 'title' | 'axis';
  placeholder?: string;
}

const EditableText: React.FC<EditableTextProps> = ({ value, onChange, variant = 'title', placeholder = 'Click to edit' }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value);

  const handleClick = () => {
    setIsEditing(true);
    setTempValue(value);
  };

  const handleClickAway = () => {
    setIsEditing(false);
    if (tempValue !== value) {
      onChange(tempValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      onChange(tempValue);
    }
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box sx={{
        display: 'inline-block',
        width: '100%',
        maxWidth: variant === 'title' ? '100%' : '150px',
        cursor: 'pointer',
        padding: variant === 'axis' ? '3px 6px' : '2px',
        borderRadius: '4px',
        backgroundColor: variant === 'axis' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',
        boxShadow: variant === 'axis' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',
        '&:hover': {
          backgroundColor: variant === 'axis' ? 'rgba(255, 255, 255, 1)' : 'rgba(0, 0, 0, 0.04)',
          boxShadow: variant === 'axis' ? '0 2px 4px rgba(0,0,0,0.15)' : 'none',
        }
      }}>
        {isEditing ? (
          <TextField
            autoFocus
            value={tempValue}
            onChange={(e) => setTempValue(e.target.value)}
            onKeyDown={handleKeyDown}
            variant="standard"
            fullWidth
            sx={{
              '& .MuiInputBase-input': {
                fontSize: variant === 'title' ? { xs: '0.9rem', sm: '1.1rem', md: '1.2rem' } : { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
                fontWeight: variant === 'title' ? 500 : 500,
                textAlign: 'center',
                padding: '2px 4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }
            }}
          />
        ) : (
          <Typography
            onClick={handleClick}
            sx={{
              fontSize: variant === 'title' ? { xs: '0.9rem', sm: '1.1rem', md: '1.2rem' } : { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
              fontWeight: variant === 'title' ? 500 : 500,
              textAlign: 'center',
              color: value ? (variant === 'axis' ? '#000000' : 'inherit') : 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {value || placeholder}
          </Typography>
        )}
      </Box>
    </ClickAwayListener>
  );
};

const Chart: React.FC<ChartProps> = ({ data, onChartUpdate}) => {
  // const [isLoadingInsights, setIsLoadingInsights] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const { chart_type, data: chartData, layout = {} } = data;
  const plotContainerRef = useRef<HTMLDivElement>(null); // Ref for container size access

  // Analytics hook
  const { trackChartView, trackChartDownload, trackChartCopy, trackChartEvent } = useAnalytics();

  // Function to safely extract text from title objects
  const getTitleText = (title: string | { text: string } | undefined): string => {
    if (typeof title === 'string') {
      return title;
    }
    if (typeof title === 'object' && title !== null && typeof title.text === 'string') {
      return title.text;
    }
    return ''; // Default empty string
  };

  // Initialize state directly from the potentially complex layout object
  const [chartTitle, setChartTitle] = useState(getTitleText(layout.title));
  const [xAxisLabel, setXAxisLabel] = useState(getTitleText(layout.xaxis?.title));
  const [yAxisLabel, setYAxisLabel] = useState(getTitleText(layout.yaxis?.title));

  const handleTitleChange = useCallback((newTitle: string) => {
    setChartTitle(newTitle);
    if (onChartUpdate) {
      const updatedFields = {
        chart_title: newTitle,
        x_axis_title: xAxisLabel,
        y_axis_title: yAxisLabel
      };
      onChartUpdate(updatedFields);
    }
  }, [onChartUpdate, xAxisLabel, yAxisLabel]);

  const handleXAxisChange = useCallback((newLabel: string) => {
    setXAxisLabel(newLabel);
    if (onChartUpdate) {
      const updatedFields = {
        chart_title: chartTitle,
        x_axis_title: newLabel,
        y_axis_title: yAxisLabel
      };
      onChartUpdate(updatedFields);
    }
  }, [onChartUpdate, chartTitle, yAxisLabel]);

  const handleYAxisChange = useCallback((newLabel: string) => {
    setYAxisLabel(newLabel);
    if (onChartUpdate) {
      const updatedFields = {
        chart_title: chartTitle,
        x_axis_title: xAxisLabel,
        y_axis_title: newLabel
      };
      onChartUpdate(updatedFields);
    }
  }, [onChartUpdate, chartTitle, xAxisLabel]);

  // Update state if the incoming data prop changes (e.g., after an update)
  useEffect(() => {
    setChartTitle(getTitleText(layout.title));
    setXAxisLabel(getTitleText(layout.xaxis?.title));
    setYAxisLabel(getTitleText(layout.yaxis?.title));
  }, [data, layout]); // Dependency array includes the whole data object

  // Track chart view when component mounts or data changes
  useEffect(() => {
    if (data.id && chart_type) {
      trackChartView(chart_type, data.id, {
        chart_title: getTitleText(layout.title),
        has_x_axis: !!getTitleText(layout.xaxis?.title),
        has_y_axis: !!getTitleText(layout.yaxis?.title),
        chart_group: data.chart_group
      });
    }
  }, [data.id, chart_type, trackChartView, layout.title, layout.xaxis?.title, layout.yaxis?.title, data.chart_group]);

  // --- End Get Available Fields ---

  // Uncomment this code block when insights feature is ready
  /*
  const handleInsightsRequest = useCallback(async () => {
    try {
      setIsLoadingInsights(true);

      // Prepare aggregate data from the chart
      const aggregateData = {
        chart_id: data.id,
        chart_type: chart_type,
        chart_data: chartData,
        // Add any additional aggregated data that might be useful
        summary: {
          title: chartTitle,
          x_axis: xAxisLabel,
          y_axis: yAxisLabel
        }
      };

      // If callback is provided, use it (this will use the ChartGrid's implementation)
      if (onInsightsRequest) {
        const insights = await onInsightsRequest(data.id, aggregateData, position);
        setIsLoadingInsights(false);
        return insights;
      }

      // Otherwise make direct API call using our service
      const insights = await getChartInsights(data.id, aggregateData);
      console.log('Chart insights:', insights);

      // Show success message
      setSnackbarMessage('Insights generated successfully');
      setSnackbarOpen(true);

      setIsLoadingInsights(false);
      return insights;

    } catch (error) {
      console.error('Error getting chart insights:', error);
      setIsLoadingInsights(false);

      // Show error message
      setSnackbarMessage('Failed to generate insights');
      setSnackbarOpen(true);

      return null;
    }
  }, [data.id, chart_type, chartData, chartTitle, xAxisLabel, yAxisLabel, position, onInsightsRequest]);
  */

  // Function to copy chart to clipboard
  const handleCopyChart = useCallback(async () => {
    if (!plotContainerRef.current) return;

    try {
      // Track copy attempt
      await trackChartCopy(chart_type, data.id, {
        chart_title: getTitleText(layout.title),
        chart_group: data.chart_group
      });

      // Hide action buttons temporarily
      const actionButtons = plotContainerRef.current.querySelector('.action-buttons');
      let originalDisplay = 'flex';
      if (actionButtons instanceof HTMLElement) {
        originalDisplay = actionButtons.style.display || 'flex';
        actionButtons.style.display = 'none';
      }

      // Create PNG
      const dataUrl = await toPng(plotContainerRef.current, {
        backgroundColor: 'transparent',
        quality: 1.0,
        pixelRatio: 2,
      });

      // Restore action buttons display
      if (actionButtons instanceof HTMLElement) {
        actionButtons.style.display = originalDisplay;
      }

      // Create a temporary image element
      const img = document.createElement('img');
      img.src = dataUrl;

      // When the image loads, copy it to clipboard
      img.onload = async () => {
        try {
          // Create a canvas element
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw the image on the canvas
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(img, 0, 0);

            // Convert canvas to blob and copy to clipboard
            canvas.toBlob(async (blob) => {
              if (blob) {
                try {
                  // Use the Clipboard API to copy the image
                  const item = new ClipboardItem({ 'image/png': blob });
                  await navigator.clipboard.write([item]);

                  // Track successful copy
                  await trackChartEvent('CHART_COPY', chart_type, data.id, {
                    success: true,
                    format: 'png',
                    chart_title: getTitleText(layout.title)
                  });

                  // Show success message
                  setSnackbarMessage('Chart copied to clipboard');
                  setSnackbarOpen(true);
                } catch (clipboardError) {
                  console.error('Error copying to clipboard:', clipboardError);

                  // Track copy failure
                  await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
                    error_type: 'clipboard_copy_failed',
                    error_message: clipboardError instanceof Error ? clipboardError.message : 'Unknown error'
                  });

                  setSnackbarMessage('Failed to copy chart to clipboard');
                  setSnackbarOpen(true);
                }
              }
            }, 'image/png');
          }
        } catch (canvasError) {
          console.error('Error creating canvas:', canvasError);

          // Track canvas error
          await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
            error_type: 'canvas_creation_failed',
            error_message: canvasError instanceof Error ? canvasError.message : 'Unknown error'
          });

          setSnackbarMessage('Failed to copy chart to clipboard');
          setSnackbarOpen(true);
        }
      };
    } catch (error) {
      console.error('Error copying chart to clipboard:', error);

      // Track general copy error
      await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
        error_type: 'chart_copy_failed',
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });

      setSnackbarMessage('Failed to copy chart to clipboard');
      setSnackbarOpen(true);
    }
  }, [chart_type, data.id, data.chart_group, layout.title, trackChartCopy, trackChartEvent]);

  // Function to export chart as PNG
  const handleSaveChart = useCallback(async () => {
    if (!plotContainerRef.current) return;

    try {
      // Get the chart title for the filename
      const chartTitle = getTitleText(data.layout.title) || 'chart';
      const filename = `${chartTitle.toLowerCase().replace(/\s+/g, '_')}.png`;

      // Track download attempt
      await trackChartDownload(chart_type, data.id, 'png', {
        chart_title: chartTitle,
        filename: filename,
        chart_group: data.chart_group
      });

      // Hide action buttons temporarily
      const actionButtons = plotContainerRef.current.querySelector('.action-buttons');
      let originalDisplay = 'flex';
      if (actionButtons instanceof HTMLElement) {
        originalDisplay = actionButtons.style.display || 'flex';
        actionButtons.style.display = 'none';
      }

      // Create PNG
      const dataUrl = await toPng(plotContainerRef.current, {
        backgroundColor: 'transparent',
        quality: 1.0,
        pixelRatio: 2,
      });

      // Restore action buttons display
      if (actionButtons instanceof HTMLElement) {
        actionButtons.style.display = originalDisplay;
      }

      // Create download link
      const link = document.createElement('a');
      link.download = filename;
      link.href = dataUrl;
      link.click();

      // Track successful download
      await trackChartEvent('CHART_DOWNLOAD', chart_type, data.id, {
        success: true,
        format: 'png',
        filename: filename,
        chart_title: chartTitle
      });

    } catch (error) {
      console.error('Error saving chart as PNG:', error);

      // Track download error
      await trackChartEvent('ERROR_OCCURRED', chart_type, data.id, {
        error_type: 'chart_download_failed',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        format: 'png'
      });

      setSnackbarMessage('Failed to save chart as PNG');
      setSnackbarOpen(true);
    }
  }, [data.layout.title, chart_type, data.id, data.chart_group, trackChartDownload, trackChartEvent]);

  // --- Prepare Data Logic ---
   const prepareData = useCallback(() => {
       let plotData = Array.isArray(chartData) ? chartData : [chartData]; // Ensure it's an array of traces

       // Handle specific chart type transformations
       const isDonutChart = ['donut', 'doughnut'].includes(chart_type.toLowerCase());
       if (chart_type === 'pie' || isDonutChart) {
            // Plotly pie needs 'labels' and 'values'
            return plotData.map(trace => ({
                ...trace, // Keep other properties like name, hoverinfo etc.
                type: 'pie',
                labels: trace.labels || trace.x, // Use labels if present, otherwise fallback to x
                values: trace.values || trace.y, // Use values if present, otherwise fallback to y
                // Ensure x and y are removed if they were used as source, to avoid conflicts
                x: undefined,
                y: undefined,
                hole: isDonutChart ? 0.4 : 0, // Make donuts distinct with a hole
                automargin: true, // Helps fit labels
            }));
        }

       if (chart_type === 'heatmap') {
           return plotData.map(trace => ({
               ...trace,
               type: 'heatmap',
               // Ensure z, x, y are correctly assigned if not already
               z: trace.z || trace.values, // Assuming z or values holds the heatmap data
               x: trace.x || trace.labels, // Columns
               y: trace.y, // Rows
               colorscale: 'Viridis', // Example colorscale
               // Display numeric values inside heatmap cells
               text: trace.z || trace.values, // Use the same data for text display
               texttemplate: '%{text}', // Show the actual values
               textfont: {
                   size: 12,
                   color: 'white' // White text for better contrast
               },
               hovertemplate: '<b>%{x}</b><br><b>%{y}</b><br>Value: %{z}<extra></extra>',
           }));
       }

       if (chart_type === 'boxplot') {
           return plotData.map(trace => ({
               ...trace,
               type: 'box',
               // y usually contains the numerical data for box plots
               y: trace.y || trace.values,
               x: trace.x || trace.labels // x can be used for grouping categories
           }));
       }

       // Handle sunburst chart
       if (chart_type.toLowerCase() === 'sunburst') {
           return plotData.map(trace => {
               // No need to calculate total value for the center display anymore

               return {
                   ...trace,
                   type: 'sunburst',
                   // Sunburst charts typically use labels, parents, values
                   labels: trace.labels || trace.x,
                   parents: trace.parents || [],
                   values: trace.values || trace.y,
                   // Remove x and y to avoid conflicts
                   x: undefined,
                   y: undefined,
                   branchvalues: 'total',
                   // Add white lines between sectors for better visual separation
                   marker: {
                       line: { width: 3, color: 'white' } // Increased width for better visibility
                   },
                   // Improve text contrast and visibility
                   insidetextfont: { color: '#ffffff', size: 12 },
                   outsidetextfont: { color: '#000000', size: 12 },
                   // Create a hole in the center for displaying the total
                   hole: 0.5, // Increased hole size for better visibility of the total
                   // Configure text display for sectors
                   textinfo: 'label',
                   hoverinfo: 'label+value+percent parent',
                   // Don't show text in the center (we'll use annotations instead)
                   insidetextorientation: 'radial'
               };
           });
       }

       // Handle icicle chart
       if (chart_type.toLowerCase() === 'icicle') {
           return plotData.map(trace => {
               // No need to calculate total here as we're using the global total

               return {
                   ...trace,
                   type: 'icicle',
                   // Icicle charts typically use labels, parents, values
                   labels: trace.labels || trace.x,
                   parents: trace.parents || [],
                   values: trace.values || trace.y,
                   // Remove x and y to avoid conflicts
                   x: undefined,
                   y: undefined,
                   branchvalues: 'total',
                   // Add white lines between sectors for better visual separation
                   marker: {
                       line: { width: 2, color: 'white' }
                   },
                   // Improve text contrast and visibility
                   insidetextfont: { color: '#ffffff', size: 12 },
                   outsidetextfont: { color: '#000000', size: 12 },
                   // Configure text display for sectors
                   textinfo: 'label',
                   hoverinfo: 'label+value+percent parent',
                   // Root label settings
                   root: {
                       color: 'rgba(255,255,255,0)',
                       label: '', // Remove 'Total' text from root to avoid duplication
                       text: ''   // Remove formatted total from root to avoid duplication
                   },
                   pathbar: {
                       visible: false
                   }
               };
           });
       }

       // Handle treemap chart
       if (chart_type.toLowerCase() === 'treemap') {
           return plotData.map(trace => {
               return {
                   ...trace,
                   type: 'treemap',
                   // Treemap charts typically use labels, parents, values
                   labels: trace.labels || trace.x,
                   parents: trace.parents || [],
                   values: trace.values || trace.y,
                   // Remove x and y to avoid conflicts
                   x: undefined,
                   y: undefined,
                   branchvalues: 'total',
                   // Add white lines between sectors for better visual separation
                   marker: {
                       line: { width: 1, color: 'white' }
                   },
                   // Configure text display for sectors
                   textinfo: 'label',
                   hoverinfo: 'label+value+percent parent',
                   // Root label settings
                   root: {
                       color: 'rgba(255,255,255,0)',
                       label: ''
                   }
               };
           });
       }

       // Handle bar charts with numeric value display
       if (chart_type === 'bar') {
           return plotData.map(trace => ({
               ...trace,
               type: 'bar',
               // Display numeric values on top of bars
               text: trace.y || trace.values, // Use y values for text display
               textposition: 'outside', // Position text outside the bars
               textfont: {
                   size: 10,
                   color: '#212121' // Dark text for better readability
               },
               hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
           }));
       }

       // Handle line charts with numeric value display
       if (chart_type === 'line' || chart_type === 'scatter') {
           return plotData.map(trace => ({
               ...trace,
               type: 'scatter',
               mode: trace.mode || 'lines+markers+text', // Include text mode
               // Display numeric values next to data points
               text: trace.y || trace.values, // Use y values for text display
               textposition: 'top center', // Position text above the points
               textfont: {
                   size: 9,
                   color: '#212121' // Dark text for better readability
               },
               hovertemplate: '<b>%{x}</b><br>Value: %{y}<extra></extra>',
           }));
       }

       // General case: pass through, ensure type is set if missing based on chart_type
       return plotData.map(trace => ({
           ...trace,
           type: trace.type || chart_type, // Use trace type if specified, else fallback to main chart_type
       }));

   }, [chartData, chart_type]);
   // --- End Prepare Data ---


  // --- Get Layout Logic ---
  const getLayout = useCallback(() => {
    // Start with the layout provided by the API/props
    const baseLayout = {
      ...layout, // Spread the original layout first
      title: { // Hide the Plotly title since we're using our own editable title
        text: '', // Empty text to hide the title
        font: { size: 16, color: '#212121', family: 'Arial, sans-serif' },
        x: 0.5, xanchor: 'center', y: 0.95, yanchor: 'top'
      },
      xaxis: {
        ...(layout.xaxis || {}), // Spread original xaxis settings
        title: { // Hide the Plotly x-axis title since we're using our own editable title
          text: '', // Empty text to hide the title
          font: { size: 12, color: '#212121', family: 'Arial, sans-serif' },
          standoff: 15 // Space between label and axis
        },
        tickfont: { size: 9, color: '#424242' },
        gridcolor: 'rgba(0, 0, 0, 0.1)',
        zerolinecolor: 'rgba(0, 0, 0, 0.2)',
        automargin: true, // Let Plotly adjust margin for labels
      },
      yaxis: {
        ...(layout.yaxis || {}), // Spread original yaxis settings
        title: { // Hide the Plotly y-axis title since we're using our own editable title
          text: '', // Empty text to hide the title
          font: { size: 12, color: '#212121', family: 'Arial, sans-serif' },
          standoff: 15
        },
        tickfont: { size: 9, color: '#424242' },
        gridcolor: 'rgba(0, 0, 0, 0.1)',
        zerolinecolor: 'rgba(0, 0, 0, 0.2)',
        automargin: true,
        ticklen: 4,
        tickwidth: 1,
        showgrid: true,
        fixedrange: false,
        autorange: true,
        side: 'left',
      },
      // Default settings for background, font, margins, legend
      paper_bgcolor: 'transparent', // Make background transparent
      plot_bgcolor: 'transparent', // Make plot area transparent
      font: {
        family: 'Arial, sans-serif',
        color: '#212121', // Default text color
        size: 12
      },
      margin: { // Uniform increased margins for all chart types
        l: 120, r: 30, b: 70, t: 50, pad: 4
      },
      legend: {
          ...(layout.legend || {}), // Keep original legend settings if any
          font: { size: 10, color: '#212121' },
          bgcolor: 'rgba(255,255,255,0.9)', // Slightly transparent background for legend
          bordercolor: 'rgba(0, 0, 0, 0.1)'
      },
      autosize: true, // IMPORTANT: Allow Plotly to resize with container
      // Remove fixed width/height from layout - let CSS handle it
      width: undefined,
      height: undefined,
    };

     // Specific adjustments
     const isHierarchyChart = ['treemap', 'sunburst', 'icicle'].includes(chart_type.toLowerCase());
     const isDonutChart = ['donut', 'doughnut'].includes(chart_type.toLowerCase());
     const isPieOrDonut = chart_type === 'pie' || isDonutChart;

     if (isPieOrDonut || isHierarchyChart) {
         baseLayout.margin = { l: 30, r: 30, b: 30, t: 60, pad: 4 }; // Minimal margins for pie/donut and hierarchy charts
         (baseLayout as any).showlegend = true; // Usually want legend for these chart types
         baseLayout.legend = { ...baseLayout.legend, orientation: 'v', x: 1.05, y: 0.5 }; // Place legend vertically to the right

         // Hide axis completely for pie/donut and hierarchy charts
         (baseLayout as any).xaxis = {
             ...(baseLayout as any).xaxis,
             showticklabels: false,
             showgrid: false,
             zeroline: false
         };

         (baseLayout as any).yaxis = {
             ...(baseLayout as any).yaxis,
             showticklabels: false,
             showgrid: false,
             zeroline: false
         };
     }

     // Calculate total for all traces - used by all hierarchy charts
     let totalSum = 0;
     if (Array.isArray(chartData)) {
         chartData.forEach(trace => {
             const values = trace.values || trace.y || [];
             if (Array.isArray(values)) {
                 totalSum += values.reduce((sum, val) => sum + (Number(val) || 0), 0);
             }
         });
     } else if (chartData) {
         const values = chartData.values || chartData.y || [];
         if (Array.isArray(values)) {
             totalSum += values.reduce((sum, val) => sum + (Number(val) || 0), 0);
         }
     }

     // Format total for use in annotations if needed
     // const formattedTotal = totalSum.toLocaleString(); // Commented out as it's not used anymore

     // Additional specific settings for sunburst charts
     if (chart_type.toLowerCase() === 'sunburst') {
         // Enhance sunburst chart appearance
         (baseLayout as any).sunburstcolorway = [
             '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
             '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
         ]; // Custom color palette

         // Remove Total text and count from center
         (baseLayout as any).annotations = [];
     }

     // Additional specific settings for treemap charts
     if (chart_type.toLowerCase() === 'treemap') {
         // Enhance treemap chart appearance
         (baseLayout as any).treemapcolorway = [
             '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
             '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
         ]; // Custom color palette

         // Add margin to ensure the total label is visible
         baseLayout.margin = {
             ...baseLayout.margin,
             t: 50, // Top margin
             l: 10, // Left margin
             r: 10, // Right margin
             b: 10  // Bottom margin
         };

         // Remove Total text and count
         (baseLayout as any).annotations = [];
     }

     // Additional specific settings for icicle charts
     if (chart_type.toLowerCase() === 'icicle') {
         // Enhance icicle chart appearance
         (baseLayout as any).iciclecolorway = [
             '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
             '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
         ]; // Custom color palette

         // Remove Total text and count
         (baseLayout as any).annotations = [];
     }

    return baseLayout;
  }, [layout, chartTitle, xAxisLabel, yAxisLabel, chart_type]);
   // --- End Get Layout ---

  // Handle potential errors or empty data
  if (!chartData || (Array.isArray(chartData) && chartData.length === 0) || (!Array.isArray(chartData) && Object.keys(chartData).length === 0)) {
     return <Box sx={{ p: 3, color: 'text.secondary', textAlign: 'center' }}>No data available for this chart.</Box>;
   }

  return (
    <Box sx={{ position: 'relative', width: '100%', height: '100%', p: 2 }} ref={plotContainerRef}>
      {/* Chart Title */}
      <Box sx={{
        position: 'absolute',
        top: 5,
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 2,
        width: '90%',
        textAlign: 'center',
        padding: '5px 0'
      }}>
        <EditableText
          value={chartTitle}
          onChange={handleTitleChange}
          variant="title"
          placeholder="Add Chart Title"
        />
      </Box>

      {/* X-Axis Label - Hidden for pie/donut/doughnut charts and hierarchy charts */}
      {chart_type !== 'pie' && !['donut', 'doughnut'].includes(chart_type.toLowerCase()) && !['treemap', 'sunburst', 'icicle'].includes(chart_type.toLowerCase()) && (
        <Box sx={{
          position: 'absolute',
          bottom: 10,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 2
        }}>
          <EditableText
            value={xAxisLabel}
            onChange={handleXAxisChange}
            variant="axis"
            placeholder="Add X-Axis Label"
          />
        </Box>
      )}

      {/* Y-Axis Label - Hidden for pie/donut/doughnut charts and hierarchy charts */}
      {chart_type !== 'pie' && !['donut', 'doughnut'].includes(chart_type.toLowerCase()) && !['treemap', 'sunburst', 'icicle'].includes(chart_type.toLowerCase()) && (
        <Box sx={{
          position: 'absolute',
          left: { xs: 15, sm: 15, md: 15 },
          top: '50%',
          transform: 'translateY(-50%) rotate(-90deg)',
          transformOrigin: 'center',
          zIndex: 10, // Higher z-index to ensure visibility
          // No background or shadow here - moved to EditableText component
        }}>
          <EditableText
            value={yAxisLabel}
            onChange={handleYAxisChange}
            variant="axis"
            placeholder="Add Y-Axis Label"
          />
        </Box>
      )}

      {/* Action Buttons - Positioned in bottom left corner */}
      <Box
        className="action-buttons"
        sx={{
          position: 'absolute',
          bottom: 10,
          left: 10,
          zIndex: 2,
          display: 'flex',
          gap: 0.5
        }}>
        {/* Insights Button */}
        {/* <Tooltip title="Get Insights">
          <Box
            component="button"
            onClick={handleInsightsRequest}
            sx={{
              cursor: 'pointer',
              background: 'rgba(255, 255, 255, 0.9)',
              border: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
            <InsightsIcon sx={{ fontSize: '1.2rem', color: '#2196f3' }} />
          </Box>
        </Tooltip> */}

        {/* Copy to Clipboard Button */}
        <Tooltip title="Copy to Clipboard">
          <Box
            component="button"
            onClick={handleCopyChart}
            sx={{
              cursor: 'pointer',
              background: 'rgba(255, 255, 255, 0.9)',
              border: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
            <ContentCopyIcon sx={{ fontSize: '1.2rem', color: '#4caf50' }} />
          </Box>
        </Tooltip>

        {/* Export Button */}
        <Tooltip title="Export as PNG">
          <Box
            component="button"
            onClick={handleSaveChart}
            sx={{
              cursor: 'pointer',
              background: 'rgba(255, 255, 255, 0.9)',
              border: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }
            }}
          >
            <DownloadIcon sx={{ fontSize: '1.2rem', color: '#ff9800' }} />
          </Box>
        </Tooltip>
      </Box>

      {/* Plotly Chart */}
      <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
        <Plot
          data={prepareData()}
          layout={getLayout()}
          useResizeHandler={true}
          style={{ width: '100%', height: '100%' }}
          config={{
            responsive: true,
            displayModeBar: false,
            displaylogo: false,
          }}
        />

        {/* Loading Overlay */}
        {/* {isLoadingInsights && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              zIndex: 10,
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <CircularProgress size={40} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                Generating insights...
              </Typography>
            </Box>
          </Box>
        )} */}
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity="success"
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Chart;
