import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import ChartGrid from './ChartGrid';
import ChartGroupPage from './ChartGroupPage';
import DatasetIcon from '@mui/icons-material/Dataset';
import ProfessionalButton from './common/ProfessionalButton';

interface ChartsPageProps {
  charts: any[];
  availableColumns: string[];
  onChartUpdate: (id: string | number, updatedChartData: any) => void;
  gridBackgroundColor?: string;
  onSwitchToDataPage: () => void;
}

const ChartsPage: React.FC<ChartsPageProps> = ({
  charts,
  availableColumns,
  onChartUpdate,
  gridBackgroundColor = 'white',
  onSwitchToDataPage
}) => {
  const [currentView, setCurrentView] = useState<'dashboard' | 'group'>('dashboard');
  const [selectedGroup, setSelectedGroup] = useState<{ name: string; charts: any[] } | null>(null);

  const handleGroupClick = (groupName: string, groupCharts: any[]) => {
    setSelectedGroup({ name: groupName, charts: groupCharts });
    setCurrentView('group');
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedGroup(null);
  };

  const handleGroupChartUpdate = (index: number, updatedChart: any) => {
    if (selectedGroup) {
      const updatedCharts = [...selectedGroup.charts];
      updatedCharts[index] = updatedChart;
      setSelectedGroup({ ...selectedGroup, charts: updatedCharts });

      // Also update the main charts array
      const originalIndex = charts.findIndex(chart => chart.id === updatedChart.id);
      if (originalIndex !== -1) {
        onChartUpdate(originalIndex, updatedChart);
      }
    }
  };

  // Render group page if a group is selected
  if (currentView === 'group' && selectedGroup) {
    return (
      <ChartGroupPage
        groupName={selectedGroup.name}
        charts={selectedGroup.charts}
        onBack={handleBackToDashboard}
        onChartUpdate={handleGroupChartUpdate}
        gridBackgroundColor="white"
      />
    );
  }

  // Render main charts page
  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 3,
      backgroundColor: 'white',
      minHeight: '100vh',
    }}>
      {/* Page Header - Only show when charts are available */}
      {charts.length > 0 && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
        }}>
          <Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 'bold',
                color: 'text.primary',
                mb: 1,
              }}
            >
              Your Charts
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: 'text.secondary',
              }}
            >
              {charts.length} chart{charts.length !== 1 ? 's' : ''} generated from your data
            </Typography>
          </Box>

          <ProfessionalButton
            variant="outlined"
            startIcon={<DatasetIcon />}
            onClick={onSwitchToDataPage}
          >
            Upload New Data
          </ProfessionalButton>
        </Box>
      )}

      {/* Charts Section */}
      {charts.length > 0 ? (
        <Box sx={{
          width: '100%',
          backgroundColor: 'white',
          p: 3,
          flexGrow: 1,
          overflow: 'auto',
        }}>
          <ChartGrid
            charts={charts}
            availableColumns={availableColumns}
            onChartUpdate={onChartUpdate}
            onGroupClick={handleGroupClick}
            gridBackgroundColor={gridBackgroundColor}
          />
        </Box>
      ) : (
        /* No Charts - Centered Square Box Same Size as Upload */
        <Box sx={{
          width: '100%',
          height: '100%',
          display: 'flex',

          minHeight: '100vh',
        }}>
          <Box sx={{
            width: '400px',
            height: '400px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
            borderRadius: '16px',
            p: 4,
            boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
            border: '1px solid rgba(0,0,0,0.1)',
            transition: 'transform 0.4s ease, box-shadow 0.4s ease',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
            },
          }}>
            <DatasetIcon sx={{ fontSize: 80, color: 'grey.400', mb: 3 }} />
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                textAlign: 'center',
                mb: 4,
                fontWeight: 500,
              }}
            >
              No charts available
            </Typography>
            <ProfessionalButton
              variant="primary"
              startIcon={<DatasetIcon />}
              onClick={onSwitchToDataPage}
              sx={{
                px: 4,
                py: 1.5,
              }}
            >
              Bring Your Data
            </ProfessionalButton>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default ChartsPage;
