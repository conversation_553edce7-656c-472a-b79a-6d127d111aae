# Analytics Integration Guide

## Overview

This application now includes comprehensive analytics tracking to monitor user behavior and improve the user experience. The analytics system tracks various user actions including file uploads, chart interactions, navigation, and errors.

## Features Implemented

### 📊 User Profile Tracking
- ✅ Automatic user profile creation/update on Google login
- ✅ Tracks: email, first/last login dates, login count, user agent, IP, location, name, picture
- ✅ Async processing (non-blocking)
- ✅ Unique email constraint with update logic

### 📈 Event Tracking
- ✅ File upload events
- ✅ Chart interactions (save, download, view, update, copy)
- ✅ Page view events
- ✅ Session tracking
- ✅ Navigation tracking
- ✅ Error tracking
- ✅ Custom metadata support
- ✅ Async processing (non-blocking)

### 🔗 API Endpoints
- ✅ `POST /v1/analytics/events` - General event tracking
- ✅ `POST /v1/analytics/events/file-upload` - File upload tracking
- ✅ `POST /v1/analytics/events/chart` - Chart event tracking
- ✅ `POST /v1/analytics/events/page-view` - Page view tracking
- ✅ `GET /v1/analytics/user-profile` - Get user profile
- ✅ `GET /v1/analytics/health` - Analytics health check

## Event Types Tracked

| Event Type | Description | Triggered When |
|------------|-------------|----------------|
| `USER_LOGIN` | User authentication | Google login successful |
| `FILE_UPLOAD` | File upload and processing | CSV/Excel file uploaded and processed |
| `CHART_VIEW` | Chart viewing | Chart component mounts or data changes |
| `CHART_DOWNLOAD` | Chart download | User downloads chart as PNG |
| `CHART_COPY` | Chart copy to clipboard | User copies chart to clipboard |
| `CHART_SAVE` | Chart save | User saves chart modifications |
| `CHART_UPDATE` | Chart modification | User updates chart properties |
| `CHART_INSIGHTS_REQUEST` | Insights request | User requests chart insights |
| `EXECUTIVE_SUMMARY_VIEW` | Insights viewing | User views executive summary |
| `ADDITIONAL_CHARTS_VIEW` | Group charts viewing | User clicks "More Charts" button |
| `PAGE_VIEW` | Page navigation | Component mounts with useAnalytics hook |
| `NAVIGATION` | Panel switching | User switches between data/charts panels |
| `ERROR_OCCURRED` | Error tracking | Any error occurs in the application |

## Implementation Details

### 1. Analytics Service (`src/services/apiService.ts`)

The `AnalyticsService` class provides a singleton instance for tracking events:

```typescript
import { analytics } from '../services/apiService';

// Track a general event
await analytics.trackEvent('PAGE_VIEW', { page_title: 'Dashboard' });

// Track file upload
await analytics.trackFileUpload('data.csv', 1024, 'text/csv');

// Track chart event
await analytics.trackChartEvent('CHART_DOWNLOAD', 'bar', 'chart-123');
```

### 2. React Hook (`src/hooks/useAnalytics.ts`)

The `useAnalytics` hook provides easy-to-use methods for React components:

```typescript
import useAnalytics from '../hooks/useAnalytics';

const MyComponent = () => {
  const { trackChartView, trackNavigation, trackError } = useAnalytics();
  
  // Automatically tracks page view on mount
  
  const handleChartClick = async () => {
    await trackChartView('bar', 'chart-123', { source: 'grid' });
  };
  
  return <div onClick={handleChartClick}>Chart</div>;
};
```

### 3. Automatic Tracking

Several events are tracked automatically:

- **Page Views**: Tracked when components using `useAnalytics` mount
- **User Login**: Tracked in `googleLogin` function
- **File Upload**: Tracked in `uploadCSV` function
- **Chart Views**: Tracked when Chart component mounts
- **Navigation**: Tracked when switching between panels
- **Errors**: Tracked when API calls fail

### 4. Enhanced API Functions

All API functions now include analytics tracking:

```typescript
// File upload with analytics
const uploadCSV = async (formData: FormData) => {
  // Tracks upload start, success/failure, and errors
};

// Chart insights with analytics
const getChartInsights = async (chartId, chartData) => {
  // Tracks insights request, success/failure, and response time
};

// Chart update with analytics
const updateChart = async (chartId, updatedData) => {
  // Tracks update start, success/failure, and fields changed
};
```

## Usage Examples

### Track Custom Events

```typescript
import { analytics } from '../services/apiService';

// Track a custom business event
await analytics.trackEvent('FEATURE_USED', {
  feature_name: 'advanced_filter',
  user_level: 'premium',
  timestamp: new Date().toISOString()
});
```

### Track User Actions in Components

```typescript
import useAnalytics from '../hooks/useAnalytics';

const MyComponent = () => {
  const { trackEvent, trackError } = useAnalytics();
  
  const handleButtonClick = async () => {
    try {
      // Your business logic
      await someApiCall();
      
      // Track success
      await trackEvent('BUTTON_CLICKED', {
        button_type: 'primary',
        action: 'submit_form'
      });
    } catch (error) {
      // Track error
      await trackError('api_call_failed', error.message, {
        api_endpoint: '/some/endpoint'
      });
    }
  };
  
  return <button onClick={handleButtonClick}>Submit</button>;
};
```

### Track Navigation

```typescript
const { trackNavigation } = useAnalytics();

const handlePageChange = async (newPage) => {
  await trackNavigation('dashboard', newPage, {
    user_action: 'menu_click',
    previous_time_spent: calculateTimeSpent()
  });
  
  // Navigate to new page
  setCurrentPage(newPage);
};
```

## Metadata Examples

The analytics system supports rich metadata for better insights:

```typescript
// File upload metadata
{
  file_name: 'sales_data.csv',
  file_size: 2048,
  file_type: 'text/csv',
  upload_duration: '2.3s',
  charts_generated: 5,
  chart_types: ['bar', 'line', 'pie']
}

// Chart interaction metadata
{
  chart_title: 'Monthly Sales',
  chart_group: 'sales_analysis',
  download_format: 'png',
  user_modifications: ['title_changed', 'axis_labels_added'],
  view_duration: '45s'
}

// Navigation metadata
{
  from_page: 'data',
  to_page: 'charts',
  charts_count: 3,
  auto_navigation: true,
  reason: 'successful_upload'
}
```

## Error Tracking

Errors are automatically tracked with context:

```typescript
// Automatic error tracking in API calls
{
  error_type: 'file_upload_failed',
  error_message: 'Invalid file format',
  status_code: 400,
  file_name: 'data.txt',
  user_action: 'file_upload'
}

// Manual error tracking
await trackError('validation_failed', 'Email format invalid', {
  form_field: 'email',
  user_input: 'invalid-email',
  validation_rule: 'email_format'
});
```

## Configuration

Analytics can be enabled/disabled:

```typescript
import { analytics } from '../services/apiService';

// Disable analytics (useful for development)
analytics.setEnabled(false);

// Re-enable analytics
analytics.setEnabled(true);
```

## Best Practices

1. **Non-blocking**: All analytics calls are async and won't block user interactions
2. **Error handling**: Analytics failures are logged but don't affect user experience
3. **Privacy**: Only track necessary data for improving user experience
4. **Performance**: Analytics calls are optimized and batched when possible
5. **Metadata**: Include relevant context to make analytics actionable

## Monitoring

Check analytics health:

```typescript
import { analytics } from '../services/apiService';

const healthStatus = await analytics.checkHealth();
console.log('Analytics service status:', healthStatus);
```

Get user profile:

```typescript
const userProfile = await analytics.getUserProfile();
console.log('User analytics profile:', userProfile);
```
